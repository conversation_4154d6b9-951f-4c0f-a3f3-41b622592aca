# post_management.py
from typing import List, Dict, Any, Optional
import random
from datetime import datetime, timedelta
import uuid
import re
import concurrent.futures
import threading
import logging

# --- IMPROVED: Import the async version of fetch_related_url ---
from app.utils.post_creator import (
    generate_post_from_persona_keywords, 
    fetch_related_url,
    generate_search_query_from_content,
    analyze_post_for_media,
    _parse_post_generation_response # <-- NEW: Import the centralized parser
)
from app.utils.model_initializer import model # Direct import for should_post_have_url

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def create_balanced_length_distribution(num_days: int, content_categories: List[str]) -> List[str]:
    """
    Create a balanced distribution of post lengths for scheduled posts.

    Args:
        num_days: Number of days to schedule posts for
        content_categories: List of content categories for context

    Returns:
        List of length assignments for each day
    """
    # Define length distribution strategy
    lengths = ["short", "medium", "long"]

    # Create balanced distribution patterns
    if num_days <= 7:
        # Weekly pattern: ensure variety
        base_pattern = ["short", "medium", "long", "medium", "short", "long", "medium"]
        length_distribution = (base_pattern * ((num_days // 7) + 1))[:num_days]
    elif num_days <= 30:
        # Monthly pattern: balanced with slight emphasis on medium
        # Ratio: 30% short, 50% medium, 20% long
        short_count = max(1, int(num_days * 0.3))
        long_count = max(1, int(num_days * 0.2))
        medium_count = num_days - short_count - long_count

        length_distribution = (["short"] * short_count +
                             ["medium"] * medium_count +
                             ["long"] * long_count)

        # Shuffle to avoid clustering
        random.shuffle(length_distribution)
    else:
        # Long-term pattern: maintain balance with variety
        # Create repeating pattern with some randomization
        pattern_length = 10
        base_pattern = ["short", "medium", "long", "medium", "short",
                       "medium", "long", "medium", "short", "medium"]

        full_cycles = num_days // pattern_length
        remainder = num_days % pattern_length

        length_distribution = base_pattern * full_cycles + base_pattern[:remainder]

        # Add some controlled randomization to avoid predictability
        # Swap some adjacent elements
        for i in range(0, len(length_distribution) - 1, 5):
            if i + 1 < len(length_distribution) and random.random() < 0.3:
                length_distribution[i], length_distribution[i + 1] = length_distribution[i + 1], length_distribution[i]

    return length_distribution


def optimize_length_for_category(base_length: str, category: str) -> str:
    """
    Optimize post length based on content category.

    Args:
        base_length: Base length from distribution
        category: Content category

    Returns:
        Optimized length
    """
    # Category-specific length preferences
    category_preferences = {
        "tips": "medium",           # Tips work well in medium format
        "insights": "long",         # Insights need space to develop
        "questions": "short",       # Questions should be concise
        "stories": "long",          # Stories need narrative space
        "news": "medium",           # News commentary is typically medium
        "advice": "medium",         # Advice posts are usually medium
        "personal": "long",         # Personal posts often tell stories
        "industry": "medium",       # Industry posts are typically medium
        "leadership": "medium",     # Leadership content is usually medium
        "technology": "medium",     # Tech posts are often medium
        "career": "medium",         # Career advice is typically medium
        "education": "long",        # Educational content needs space
        "motivation": "short",      # Motivational posts are often punchy
        "announcement": "short"     # Announcements should be concise
    }

    # Get category preference
    category_lower = category.lower()
    preferred_length = None

    for cat_key, pref_length in category_preferences.items():
        if cat_key in category_lower:
            preferred_length = pref_length
            break

    # If no specific preference, use base length
    if not preferred_length:
        return base_length

    # Apply preference with some flexibility
    # If base and preferred are the same, use it
    if base_length == preferred_length:
        return base_length

    # If they differ, use preferred 70% of the time, base 30% of the time
    if random.random() < 0.7:
        return preferred_length
    else:
        return base_length


def clean_html_for_query(html_content: str) -> str:
    """Remove HTML tags and extra whitespace to create a clean query string."""
    clean_text = re.sub(r'<[^>]+>', '', html_content)
    clean_text = re.sub(r'\s+', ' ', clean_text).strip()
    return clean_text

def should_post_have_url(post_content: str) -> bool:
    """Use LLM to determine if a post should include a URL."""
    prompt = (
        "Should this LinkedIn post include a relevant external URL? Respond only with true or false.\n"
        f"Post:\n{post_content}"
    )
    try:
        response = model.generate_content(prompt)
        answer = response.text.strip().lower()
        return answer.startswith('true')
    except Exception as e:
        logging.error(f"Error in should_post_have_url: {e}")
        return False

# --- NEW: Refactored helper function for calculating category distribution ---
def _calculate_category_distribution(num_days: int, categories: List[str]) -> List[str]:
    """Calculates the distribution of categories over the number of days."""
    if not categories:
        return ["General"] * num_days
        
    if num_days <= len(categories):
        distribution = categories[:num_days]
    else:
        base_count = num_days // len(categories)
        remainder = num_days % len(categories)
        distribution = categories * base_count
        if remainder > 0:
            distribution.extend(random.sample(categories, remainder))
    
    random.shuffle(distribution)
    return distribution

# --- NEW: Refactored and optimized post generation task for parallel execution ---
def _generate_and_process_post_for_day(day_index: int, start_dt: datetime, category: str, persona_data: Dict, used_opening_words: set, opening_words_lock: threading.Lock, url_budget: threading.Semaphore, assigned_length: str = "medium") -> Dict:
    """
    Generates a single post, analyzes it, and fetches a URL if needed.
    This function is designed to be run in a separate thread.
    """
    post_date = start_dt + timedelta(days=day_index)
    post_datetime_str = (post_date.replace(
        hour=random.randint(8, 18), 
        minute=random.choice([0, 15, 30, 45]))
    ).strftime("%Y-%m-%d %H:%M")

    # Optimize length for category
    optimized_length = optimize_length_for_category(assigned_length, category)

    # Build prompt for this day with length context
    custom_prompt = f"As a {', '.join(persona_data.get('general_persona_keywords', []))}, write a {persona_data.get('style', 'thought-provoking').lower()} {optimized_length} post about {category}. [Schedule Day {day_index}, Variant for uniqueness]"
    if persona_data.get('content_persona_keywords'):
        custom_prompt += f" Focus on these topics: {', '.join(persona_data['content_persona_keywords'])}."
    if persona_data.get('network_persona_keywords'):
        custom_prompt += f" Target audience: {', '.join(persona_data['network_persona_keywords'])}."

    with opening_words_lock:
        current_used_words = used_opening_words.copy()

    # Generate post content with optimized length
    raw_post_response = generate_post_from_persona_keywords(
        persona_data.get('general_persona_keywords', []),
        persona_data.get('tone', "Professional"),
        persona_data.get('style', "Informative"),
        custom_prompt,
        length=optimized_length,  # Pass the optimized length
        content_interests=persona_data.get('content_persona_keywords'),
        network_interests=persona_data.get('network_persona_keywords'),
        add_emojis=persona_data.get('add_emojis', True),
        add_hashtags=persona_data.get('add_hashtags', True),
        use_hook_generator=True,
        used_opening_words=current_used_words
    )

    # --- IMPROVED: Use centralized parser ---
    parsed_post = _parse_post_generation_response(raw_post_response)
    if not parsed_post:
        logging.warning(f"Failed to generate or parse post for day {day_index}.")
        # Return a minimal structure to avoid breaking the schedule
        return {
            "post_id": str(uuid.uuid4()),
            "scheduled_datetime": post_datetime_str,
            "content": "Content generation failed for this slot. Please regenerate.",
            "category": category,
            "day_index": day_index,
            "has_image": False, "has_infographics": False, "url": None
        }

    post_content = parsed_post["content"]

    # Track unique opening words
    if post_content:
        clean_text = clean_html_for_query(post_content)
        first_word = clean_text.split()[0].lower() if clean_text else ""
        if first_word:
            with opening_words_lock:
                if first_word not in used_opening_words:
                    used_opening_words.add(first_word)

    # Media analysis and URL fetching
    media_analysis = analyze_post_for_media(post_content)
    post_url = None

    # --- IMPROVED: Integrated URL logic within the thread ---
    if url_budget.acquire(blocking=False): # Non-blocking attempt to acquire semaphore
        if should_post_have_url(post_content):
            try:
                import asyncio
                clean_text = clean_html_for_query(post_content)
                search_query = generate_search_query_from_content(clean_text)
                # Run the async function in the current thread's event loop
                post_url = asyncio.run(fetch_related_url(search_query))
            except Exception as e:
                logging.error(f"Error fetching URL for day {day_index}: {e}")
                url_budget.release() # Release budget if URL fetch fails

    return {
        "post_id": str(uuid.uuid4()),
        "scheduled_datetime": post_datetime_str,
        "style": persona_data.get('style', "Informative"),
        "tone": persona_data.get('tone', "Professional"),
        "length": optimized_length.title(),  # Use the actual optimized length
        "content": post_content,
        "category": category,
        "has_image": media_analysis.get("has_image", False),
        "has_infographics": media_analysis.get("has_infographics", False),
        "url": post_url,
        "day_index": day_index
    }


# --- REFACTORED: Main scheduling function is now cleaner and more efficient ---
def generate_post_schedule(persona_data, schedule_duration, start_date=None, end_date=None, template=None):
    """
    Generates a post schedule using parallel processing with integrated URL fetching.
    """
    if start_date and end_date:
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        num_days = (end_dt - start_dt).days + 1
    else:
        start_dt = datetime.today()
        num_days = 7

    # Setup for parallel execution
    used_opening_words = set()
    opening_words_lock = threading.Lock()
    
    # --- IMPROVED: Use a semaphore to limit the number of URLs ---
    url_limit = 3 if num_days >= 7 else max(1, num_days // 2)
    url_budget = threading.Semaphore(url_limit)
    
    categories = persona_data.get('categories', [])
    category_distribution = _calculate_category_distribution(num_days, categories)

    # Create balanced length distribution for variety
    length_distribution = create_balanced_length_distribution(num_days, categories)
    logging.info(f"Created balanced length distribution: {dict(zip(range(num_days), length_distribution))}")

    posts_data = []
    persona_data['style'] = random.choice(["Informative", "Thought-provoking", "Personal storytelling"])

    with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, num_days)) as executor:
        future_to_day = {
            executor.submit(
                _generate_and_process_post_for_day,
                day,
                start_dt,
                category_distribution[day],
                persona_data,
                used_opening_words,
                opening_words_lock,
                url_budget,
                length_distribution[day]  # Pass the assigned length for this day
            ): day for day in range(num_days)
        }
        
        for future in concurrent.futures.as_completed(future_to_day):
            try:
                result = future.result()
                posts_data.append(result)
            except Exception as e:
                logging.error(f"A post generation task failed: {e}")

    # Final processing and cleanup
    posts_data.sort(key=lambda x: x["day_index"])
    for post in posts_data:
        del post["day_index"]

    return posts_data