# feed_categorizer.py
from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES
import json
import asyncio
from typing import List, Dict, Any, Optional
import copy

# Local definition to avoid circular import
class FeedPostItem:
    """Local definition of FeedPostItem to avoid circular import."""
    def __init__(self, activity_urn: str, text: str, total_reactions: int = 0, 
                 total_comments: int = 0, total_shares: int = 0, author_urn: str = ""):
        self.activity_urn = activity_urn
        self.text = text
        self.total_reactions = total_reactions
        self.total_comments = total_comments
        self.total_shares = total_shares
        self.author_urn = author_urn

async def categorize_feed_posts(
    posts: List[FeedPostItem],  # List of FeedPostItem objects
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """
    Categorize posts into persona-specific hierarchical categories and sub-categories, 
    and generate recommendations for each post. Filters out irrelevant posts.
    Only generates categories that have relevant posts.
    Returns the new nested output format.
    """
    print(f"\n=== FEED CATEGORIZATION START ===")
    print(f"Input posts: {len(posts)}")
    print(f"General persona keywords: {general_persona_keywords}")
    print(f"Content persona keywords: {content_persona_keywords}")
    print(f"Network persona keywords: {network_persona_keywords}")
    
    if not posts:
        print("No posts provided. Returning empty result.")
        return []

    # Step 1: Filter posts based on persona relevance (more strict filtering)
    relevant_posts = await _filter_posts_by_persona_relevance_strict(
        posts, 
        general_persona_keywords, 
        content_persona_keywords, 
        network_persona_keywords
    )

    print(f"Input posts: {len(posts)}, Relevant posts after filtering: {len(relevant_posts)}")

    if not relevant_posts:
        print("No posts found relevant to the persona theme. Returning empty result.")
        return []

    # Step 2: Generate persona-specific categories based on actual relevant posts
    if not relevant_posts:
        print("No relevant posts to categorize. Returning empty result.")
        return []
        
    persona_categories = await _generate_persona_categories_from_posts(
        relevant_posts,
        general_persona_keywords, 
        content_persona_keywords, 
        network_persona_keywords
    )

    # Step 3: Categorize relevant posts into the generated categories
    if not persona_categories:
        print("No categories generated for relevant posts. Returning empty result.")
        return []
        
    categorized_feed = await _categorize_posts_into_persona_categories(
        relevant_posts, 
        persona_categories,
        general_persona_keywords, 
        content_persona_keywords, 
        network_persona_keywords
    )

    # Step 4: Validate and clean the categorized feed
    categorized_feed = _validate_and_clean_categorized_feed(categorized_feed, relevant_posts)

    # Step 5: Remove any empty categories or sub-categories
    categorized_feed = _remove_empty_categories(categorized_feed)

    # Step 6: Final validation - ensure we don't have more posts than input
    total_output_posts = 0
    for category in categorized_feed:
        for subcat in category.get("sub_categories", []):
            total_output_posts += len(subcat.get("posts", []))
    
    if total_output_posts > len(relevant_posts):
        print(f"Warning: Output has {total_output_posts} posts but input had {len(relevant_posts)} posts. Trimming to match input.")
        # Trim posts to match input count
        posts_to_keep = len(relevant_posts)
        posts_kept = 0
        
        for category in categorized_feed:
            for subcat in category.get("sub_categories", []):
                if posts_kept >= posts_to_keep:
                    subcat["posts"] = []
                    continue
                
                current_posts = subcat.get("posts", [])
                if posts_kept + len(current_posts) <= posts_to_keep:
                    posts_kept += len(current_posts)
                else:
                    # Keep only the remaining posts needed
                    remaining_needed = posts_to_keep - posts_kept
                    subcat["posts"] = current_posts[:remaining_needed]
                    posts_kept = posts_to_keep

    # Step 7: Generate summaries and recommendations for each sub-category
    for category in categorized_feed:
        for subcat in category.get("sub_categories", []):
            subcat_posts = subcat.get("posts", [])
            if not subcat_posts:
                continue
                
            # Sort posts by engagement score (descending)
            def engagement_score(post):
                if isinstance(post, dict):
                    reactions = post.get("total_reactions", 0)
                    comments = post.get("total_comments", 0)
                    shares = post.get("total_shares", 0)
                else:
                    reactions = getattr(post, "total_reactions", 0)
                    comments = getattr(post, "total_comments", 0)
                    shares = getattr(post, "total_shares", 0)
                return reactions + comments + shares
            subcat_posts = sorted(subcat_posts, key=engagement_score, reverse=True)
            
            # Generate summary
            subcat["summary"] = await _generate_persona_category_summary(
                subcat.get("sub_categories_name", ""),
                subcat_posts,
                general_persona_keywords,
                content_persona_keywords,
                network_persona_keywords
            )
            
            # List unique authors as dicts
            author_urns = []
            seen = set()
            for post in subcat_posts:
                if isinstance(post, dict):
                    author_urn = post.get("author_urn", "Unknown")
                else:
                    author_urn = getattr(post, "author_urn", "Unknown")
                if author_urn and author_urn not in seen:
                    author_urns.append(author_urn)
                    seen.add(author_urn)
            subcat["author_member_urns"] = author_urns
            
            # For each post, add recommendations and wrap original post
            new_posts = []
            for post in subcat_posts:
                rec = await _generate_persona_post_recommendations(
                    post,
                    category.get("category_name", ""),
                    subcat.get("sub_categories_name", ""),
                    general_persona_keywords,
                    content_persona_keywords,
                    network_persona_keywords
                )
                
                # Extract activity_urn ensuring it's never empty
                if isinstance(post, dict):
                    activity_urn = post.get("activity_urn", "")
                else:
                    activity_urn = getattr(post, "activity_urn", "")
                
                # Ensure activity_urn is never empty - use a fallback if needed
                if not activity_urn:
                    activity_urn = "urn:li:activity:default"
                
                new_posts.append({
                    "activity_urn": activity_urn,
                    "recommended_reaction": rec["reaction"],
                    "suggested_comment": rec["comment"]
                })
            subcat["posts"] = new_posts

    # Final validation - count total posts in output
    total_final_posts = 0
    for category in categorized_feed:
        for subcat in category.get("sub_categories", []):
            total_final_posts += len(subcat.get("posts", []))
    
    print(f"Final output posts: {total_final_posts}")
    
    # Final check - if no posts in output, return empty
    if total_final_posts == 0:
        print("No posts in final output. Returning empty result.")
        return []

    print(f"=== FEED CATEGORIZATION COMPLETE ===")
    print(f"Input posts: {len(posts)}")
    print(f"Relevant posts after filtering: {len(relevant_posts)}")
    print(f"Final output posts: {total_final_posts}")
    print(f"Categories generated: {len(categorized_feed)}")
    print("=" * 40)

    return categorized_feed

async def _filter_posts_by_persona_relevance_strict(
    posts: List[FeedPostItem],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> List[FeedPostItem]:
    """
    Strict filtering of posts to only include those highly relevant to the persona's professional context.
    Only includes posts that are directly relevant to the persona's professional interests and expertise.
    """
    
    # Prepare posts data for relevance analysis
    posts_data = []
    for i, post in enumerate(posts):
        if isinstance(post, dict):
            post_text = post.get("text", "")
            author_urn = post.get("author_urn", "Unknown")
            activity_urn = post.get("activity_urn", "")
            total_reactions = post.get("total_reactions", 0)
            total_comments = post.get("total_comments", 0)
            total_shares = post.get("total_shares", 0)
        else:
            post_text = getattr(post, "text", "")
            author_urn = getattr(post, "author_urn", "Unknown")
            activity_urn = getattr(post, "activity_urn", "")
            total_reactions = getattr(post, "total_reactions", 0)
            total_comments = getattr(post, "total_comments", 0)
            total_shares = getattr(post, "total_shares", 0)
        
        posts_data.append({
            "id": i,
            "text": post_text,
            "author_urn": author_urn,
            "activity_urn": activity_urn,
            "total_reactions": total_reactions,
            "total_comments": total_comments,
            "total_shares": total_shares,
        })
    
    prompt = TEMPLATES["strict_persona_post_filtering"].format(
        posts_data=json.dumps(posts_data, indent=2),
        general_persona_keywords=", ".join(general_persona_keywords),
        content_persona_keywords=", ".join(content_persona_keywords) if content_persona_keywords else "None",
        network_persona_keywords=", ".join(network_persona_keywords) if network_persona_keywords else "None"
    )
    
    try:
        response = model.generate_content(prompt)
        response_text = response.text.strip()
        
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        
        # Parse the JSON
        result = json.loads(response_text)
        relevant_post_ids = result.get("relevant_post_ids", [])
        
        # Return only the relevant posts
        relevant_posts = []
        for post_id in relevant_post_ids:
            if post_id < len(posts):
                relevant_posts.append(posts[post_id])
        
        print(f"AI filtering selected {len(relevant_posts)} out of {len(posts)} posts as relevant")
        return relevant_posts
        
    except Exception as e:
        print(f"AI strict post filtering failed: {e}")
        # Fallback: use keyword-based filtering
        return _fallback_strict_post_filtering(posts_data, general_persona_keywords, posts)

async def _generate_persona_categories_from_posts(
    relevant_posts: List[FeedPostItem],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """
    Generate persona-specific categories and sub-categories based on the actual relevant posts.
    Only creates categories that have content to fill them.
    """
    
    # Prepare posts data for category generation
    posts_data = []
    for i, post in enumerate(relevant_posts):
        if isinstance(post, dict):
            post_text = post.get("text", "")
            author_urn = post.get("author_urn", "Unknown")
            activity_urn = post.get("activity_urn", "")
            total_reactions = post.get("total_reactions", 0)
            total_comments = post.get("total_comments", 0)
            total_shares = post.get("total_shares", 0)
        else:
            post_text = getattr(post, "text", "")
            author_urn = getattr(post, "author_urn", "Unknown")
            activity_urn = getattr(post, "activity_urn", "")
            total_reactions = getattr(post, "total_reactions", 0)
            total_comments = getattr(post, "total_comments", 0)
            total_shares = getattr(post, "total_shares", 0)
        
        posts_data.append({
            "id": i,
            "text": post_text,
            "author_urn": author_urn,
            "activity_urn": activity_urn,
            "total_reactions": total_reactions,
            "total_comments": total_comments,
            "total_shares": total_shares,
        })
    
    prompt = TEMPLATES["persona_categories_from_posts"].format(
        posts_data=json.dumps(posts_data, indent=2),
        general_persona_keywords=", ".join(general_persona_keywords),
        content_persona_keywords=", ".join(content_persona_keywords) if content_persona_keywords else "None",
        network_persona_keywords=", ".join(network_persona_keywords) if network_persona_keywords else "None"
    )
    
    try:
        response = model.generate_content(prompt)
        response_text = response.text.strip()
        
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        
        # Parse the JSON
        result = json.loads(response_text)
        if "categories" in result:
            return result["categories"]
        else:
            raise ValueError("No 'categories' key found in response")
            
    except Exception as e:
        print(f"AI persona categories from posts failed: {e}")
        # Fallback to keyword-based categories
        return _fallback_persona_categories_from_posts(posts_data, general_persona_keywords)

async def _categorize_posts_into_persona_categories(
    posts: List[FeedPostItem],
    persona_categories: List[Dict[str, Any]],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """
    Categorize posts into the pre-generated persona-specific categories.
    """
    
    # Prepare posts data for categorization
    posts_data = []
    for i, post in enumerate(posts):
        if isinstance(post, dict):
            post_text = post.get("text", "")
            author_urn = post.get("author_urn", "Unknown")
            activity_urn = post.get("activity_urn", "")
            total_reactions = post.get("total_reactions", 0)
            total_comments = post.get("total_comments", 0)
            total_shares = post.get("total_shares", 0)
        else:
            post_text = getattr(post, "text", "")
            author_urn = getattr(post, "author_urn", "Unknown")
            activity_urn = getattr(post, "activity_urn", "")
            total_reactions = getattr(post, "total_reactions", 0)
            total_comments = getattr(post, "total_comments", 0)
            total_shares = getattr(post, "total_shares", 0)
        
        posts_data.append({
            "id": i,
            "text": post_text,
            "author_urn": author_urn,
            "activity_urn": activity_urn,
            "total_reactions": total_reactions,
            "total_comments": total_comments,
            "total_shares": total_shares,
        })
    
    prompt = TEMPLATES["persona_post_categorization"].format(
        posts_data=json.dumps(posts_data, indent=2),
        persona_categories=json.dumps(persona_categories, indent=2),
        general_persona_keywords=", ".join(general_persona_keywords),
        content_persona_keywords=", ".join(content_persona_keywords) if content_persona_keywords else "None",
        network_persona_keywords=", ".join(network_persona_keywords) if network_persona_keywords else "None"
    )
    
    try:
        response = model.generate_content(prompt)
        response_text = response.text.strip()
        
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        
        # Parse the JSON
        result = json.loads(response_text)
        if "categorized_posts" in result:
            # Map back to original posts using the IDs
            categorized_result = result["categorized_posts"]
            
            # Replace AI-generated post data with original post data
            for category in categorized_result:
                for sub_cat in category.get("sub_categories", []):
                    original_posts = []
                    for ai_post in sub_cat.get("posts", []):
                        post_id = ai_post.get("id")
                        if post_id is not None and post_id < len(posts):
                            # Use the original post data
                            original_post = posts[post_id]
                            if isinstance(original_post, dict):
                                original_posts.append(original_post)
                            else:
                                # Convert FeedPostItem to dict
                                original_posts.append({
                                    "text": getattr(original_post, "text", ""),
                                    "author_urn": getattr(original_post, "author_urn", "Unknown"),
                                    "activity_urn": getattr(original_post, "activity_urn", ""),
                                    "total_reactions": getattr(original_post, "total_reactions", 0),
                                    "total_comments": getattr(original_post, "total_comments", 0),
                                    "total_shares": getattr(original_post, "total_shares", 0),
                                })
                    sub_cat["posts"] = original_posts
            
            return categorized_result
        else:
            raise ValueError("No 'categorized_posts' key found in response")
            
    except Exception as e:
        print(f"AI persona post categorization failed: {e}")
        # Fallback categorization
        return _fallback_persona_post_categorization(posts_data, persona_categories)

def _validate_and_clean_categorized_feed(categorized_feed: List[Dict[str, Any]], original_posts: List[FeedPostItem]) -> List[Dict[str, Any]]:
    """
    Validate and clean the categorized feed to ensure:
    1. No duplicate posts
    2. No posts with default activity URNs
    3. All posts are from the original input
    4. Number of posts doesn't exceed input count
    """
    seen_posts = set()
    cleaned_feed = []
    original_activity_urns = set()
    
    # Create set of original activity URNs for validation
    for post in original_posts:
        if isinstance(post, dict):
            activity_urn = post.get("activity_urn", "")
        else:
            activity_urn = getattr(post, "activity_urn", "")
        if activity_urn:
            original_activity_urns.add(activity_urn)
    
    for category in categorized_feed:
        cleaned_category = {
            "category_name": category.get("category_name", ""),
            "sub_categories": []
        }
        
        for sub_cat in category.get("sub_categories", []):
            cleaned_sub_cat = {
                "sub_categories_name": sub_cat.get("sub_categories_name", ""),
                "posts": []
            }
            
            for post in sub_cat.get("posts", []):
                # Extract activity_urn
                if isinstance(post, dict):
                    activity_urn = post.get("activity_urn", "")
                else:
                    activity_urn = getattr(post, "activity_urn", "")
                
                # Skip posts with default activity URN, already seen posts, or not in original
                if (activity_urn == "urn:li:activity:default" or 
                    activity_urn in seen_posts or 
                    activity_urn not in original_activity_urns):
                    continue
                
                # Add to seen posts
                seen_posts.add(activity_urn)
                
                # Add to cleaned sub-category
                cleaned_sub_cat["posts"].append(post)
            
            # Only add sub-categories with posts
            if cleaned_sub_cat["posts"]:
                cleaned_category["sub_categories"].append(cleaned_sub_cat)
        
        # Only add categories with sub-categories
        if cleaned_category["sub_categories"]:
            cleaned_feed.append(cleaned_category)
    
    return cleaned_feed

def _remove_empty_categories(categorized_feed: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Remove categories and sub-categories that have no posts.
    """
    # Remove empty sub-categories
    for category in categorized_feed:
        category["sub_categories"] = [
            subcat for subcat in category.get("sub_categories", [])
            if subcat.get("posts", [])
        ]
    
    # Remove empty categories
    categorized_feed = [
        category for category in categorized_feed
        if category.get("sub_categories", [])
    ]
    
    return categorized_feed

async def _generate_persona_category_summary(
    category_name: str, 
    posts: List[Any],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> str:
    """
    Generate a persona-specific summary for a category.
    """
    
    # Extract post texts for summary generation
    post_texts = []
    for post in posts:
        if isinstance(post, dict):
            post_texts.append(post.get("text", ""))
        else:
            post_texts.append(getattr(post, "text", ""))
    
    sample_posts = post_texts[:5]  # Use first 5 posts for summary
    
    prompt = TEMPLATES["persona_category_summary"].format(
        category_name=category_name,
        sample_posts="\n\n".join(sample_posts),
        general_persona_keywords=", ".join(general_persona_keywords),
        content_persona_keywords=", ".join(content_persona_keywords) if content_persona_keywords else "None",
        network_persona_keywords=", ".join(network_persona_keywords) if network_persona_keywords else "None"
    )
    
    try:
        response = model.generate_content(prompt)
        return response.text.strip()
    except Exception as e:
        # Fallback summary
        return f"This category contains posts specifically relevant to {category_name.lower()}, tailored to your professional interests and expertise."

async def _generate_persona_post_recommendations(
    post: Any,
    category_name: str,
    sub_category_name: str,
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> Dict[str, str]:
    """
    Generate persona-specific recommended reaction and suggested comment for a post.
    """
    
    # Handle both FeedPostItem objects and dicts
    if isinstance(post, dict):
        post_text = post.get("text", "")
        author_urn = post.get("author_urn", "Unknown")
    else:
        post_text = getattr(post, "text", "")
        author_urn = getattr(post, "author_urn", "Unknown")
    
    prompt = TEMPLATES["persona_post_recommendations"].format(
        post_content=post_text,
        author=author_urn,
        category_name=category_name,
        sub_category_name=sub_category_name,
        general_persona_keywords=", ".join(general_persona_keywords),
        content_persona_keywords=", ".join(content_persona_keywords) if content_persona_keywords else "None",
        network_persona_keywords=", ".join(network_persona_keywords) if network_persona_keywords else "None"
    )
    
    try:
        response = model.generate_content(prompt)
        response_text = response.text.strip()
        
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        
        # Try to parse the JSON
        try:
            result = json.loads(response_text)
            return {
                "reaction": result.get("recommended_reaction", "Like"),
                "comment": result.get("suggested_comment", "Great insights!")
            }
        except json.JSONDecodeError as json_error:
            print(f"JSON parsing error in recommendations: {json_error}")
            print(f"Response text: {response_text}")
            raise ValueError(f"Invalid JSON response: {json_error}")
            
    except Exception as e:
        print(f"AI persona recommendations failed: {e}")
        # Fallback recommendations
        return {
            "reaction": "Like",
            "comment": "Thanks for sharing this valuable content!"
        }

def _fallback_strict_post_filtering(posts_data: List[Dict[str, Any]], persona_keywords: List[str], original_posts: List[FeedPostItem]) -> List[FeedPostItem]:
    """
    Fallback ultra-strict filtering when AI fails.
    """
    relevant_posts = []
    keywords_lower = [kw.lower() for kw in persona_keywords]
    
    for i, post_data in enumerate(posts_data):
        content = post_data["text"].lower()
        
        # Check for keyword matches with higher threshold
        relevance_score = 0
        for keyword in keywords_lower:
            if keyword in content:
                relevance_score += 1
        
        # Only include posts with high relevance (at least 3 keyword matches for strict filtering)
        if relevance_score >= 3:  # Increased threshold for stricter filtering
            relevant_posts.append(original_posts[i])
    
    print(f"Fallback filtering selected {len(relevant_posts)} out of {len(original_posts)} posts as relevant")
    return relevant_posts

def _fallback_persona_categories_from_posts(posts_data: List[Dict[str, Any]], general_persona_keywords: List[str]) -> List[Dict[str, Any]]:
    """
    Fallback category generation from posts when AI fails.
    """
    # Analyze posts to determine categories
    categories = {}
    
    for post in posts_data:
        content = post["text"].lower()
        
        # Determine category based on content
        if any(word in content for word in ["neural", "deep learning", "machine learning", "ai", "model"]):
            if "AI & Machine Learning" not in categories:
                categories["AI & Machine Learning"] = {"Model Development": [], "AI Research": []}
            if "neural" in content or "deep learning" in content:
                categories["AI & Machine Learning"]["Model Development"].append(post)
            else:
                categories["AI & Machine Learning"]["AI Research"].append(post)
        
        elif any(word in content for word in ["ethics", "responsible", "governance"]):
            if "Ethical AI" not in categories:
                categories["Ethical AI"] = {"Responsible Development": []}
            categories["Ethical AI"]["Responsible Development"].append(post)
        
        elif any(word in content for word in ["healthcare", "finance", "sustainability", "application"]):
            if "Applied AI" not in categories:
                categories["Applied AI"] = {"Industry Applications": []}
            categories["Applied AI"]["Industry Applications"].append(post)
        
        elif any(word in content for word in ["tool", "framework", "development"]):
            if "AI Development" not in categories:
                categories["AI Development"] = {"Tools & Frameworks": []}
            categories["AI Development"]["Tools & Frameworks"].append(post)
        
        else:
            if "AI Insights" not in categories:
                categories["AI Insights"] = {"General AI": []}
            categories["AI Insights"]["General AI"].append(post)
    
    # Convert to expected format
    result = []
    for category_name, sub_categories in categories.items():
        sub_cats = []
        for sub_cat_name, posts in sub_categories.items():
            if posts:  # Only include sub-categories with posts
                sub_cats.append({
                    "sub_categories_name": sub_cat_name,
                    "posts": posts
                })
        
        if sub_cats:  # Only include categories with sub-categories
            result.append({
                "category_name": category_name,
                "sub_categories": sub_cats
            })
    
    return result

def _fallback_persona_post_categorization(
    posts_data: List[Dict[str, Any]], 
    persona_categories: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    Fallback categorization when AI categorization fails.
    """
    # Create a copy of persona categories to populate with posts
    result = copy.deepcopy(persona_categories)
    
    # Simple keyword-based categorization
    for post in posts_data:
        content = post["text"].lower()
        
        # Find the best matching category and sub-category
        best_match = None
        best_score = 0
        
        for category in result:
            category_name = category["category_name"].lower()
            for sub_cat in category["sub_categories"]:
                sub_cat_name = sub_cat["sub_categories_name"].lower()
                
                # Calculate relevance score
                score = 0
                if category_name in content:
                    score += 2
                if sub_cat_name in content:
                    score += 3
                
                # Check for keyword matches
                for word in category_name.split():
                    if word in content:
                        score += 1
                for word in sub_cat_name.split():
                    if word in content:
                        score += 1
                
                if score > best_score:
                    best_score = score
                    best_match = (category, sub_cat)
        
        # Assign post to best matching sub-category
        if best_match and best_score > 0:
            category, sub_cat = best_match
            sub_cat["posts"].append(post)
        else:
            # Default to first category and sub-category
            if result and result[0]["sub_categories"]:
                result[0]["sub_categories"][0]["posts"].append(post)
    
    return result

# Legacy functions for backward compatibility
async def _categorize_posts_hierarchical(
    posts: List[FeedPostItem],
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """
    Legacy function - now redirects to the new persona-aware categorization.
    """
    return await categorize_feed_posts(posts, general_persona_keywords, content_persona_keywords, network_persona_keywords)

async def _generate_category_summary(
    category_name: str, 
    posts: List[Any],
    general_persona_keywords: List[str]
) -> str:
    """
    Legacy function - now redirects to persona-aware summary generation.
    """
    return await _generate_persona_category_summary(category_name, posts, general_persona_keywords)

async def _generate_post_recommendations(
    post: Any,
    category_name: str,
    general_persona_keywords: List[str],
    content_persona_keywords: Optional[List[str]] = None,
    network_persona_keywords: Optional[List[str]] = None
) -> Dict[str, str]:
    """
    Legacy function - now redirects to persona-aware recommendations.
    """
    return await _generate_persona_post_recommendations(
        post, category_name, "", general_persona_keywords, content_persona_keywords, network_persona_keywords
    )

def _fallback_hierarchical_categorization(posts_data: List[Any]) -> List[Dict[str, Any]]:
    """
    Legacy fallback function - now redirects to persona-aware fallback.
    """
    return _fallback_persona_post_categorization(posts_data, _fallback_persona_categories_from_posts(posts_data, []))

def _post_process_categories_for_broadness(categories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Legacy function - no longer needed with persona-specific categorization.
    """
    return categories

def _make_sub_category_broader(sub_category_name: str) -> str:
    """
    Legacy function - no longer needed with persona-specific categorization.
    """
    return sub_category_name

def _validate_categorization_structure(categories: List[Dict[str, Any]]) -> None:
    """
    Legacy validation function - still useful for debugging.
    """
    if not isinstance(categories, list):
        raise ValueError("Categories must be a list")
    
    for category in categories:
        if not isinstance(category, dict):
            raise ValueError("Each category must be a dictionary")
        
        if "category_name" not in category:
            raise ValueError("Each category must have a 'category_name' field")
        
        if "sub_categories" not in category:
            raise ValueError("Each category must have a 'sub_categories' field")
        
        sub_categories = category["sub_categories"]
        if not isinstance(sub_categories, list):
            raise ValueError("Sub_categories must be a list")
        
        for sub_cat in sub_categories:
            if not isinstance(sub_cat, dict):
                raise ValueError("Each sub-category must be a dictionary")
            
            if "sub_categories_name" not in sub_cat:
                raise ValueError("Each sub-category must have a 'sub_categories_name' field")
            
            if "posts" not in sub_cat:
                raise ValueError("Each sub-category must have a 'posts' field")
            
            posts = sub_cat["posts"]
            if not isinstance(posts, list):
                raise ValueError("Posts must be a list")
            
            for post in posts:
                if not isinstance(post, dict):
                    raise ValueError("Each post must be a dictionary")
                
                required_fields = ["id", "text", "author_urn"]
                for field in required_fields:
                    if field not in post:
                        raise ValueError(f"Each post must have a '{field}' field") 