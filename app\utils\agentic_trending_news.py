import os
import requests
import asyncio
import random
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from app.utils.model_initializer import model
from bs4 import BeautifulSoup
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor


def extract_source_from_url(url: str) -> str:
    """
    Extract the source name from a URL.
    
    Args:
        url: The URL to extract source from
        
    Returns:
        The source name (e.g., "TechCrunch", "Reuters", "CNN")
    """
    try:
        if not url:
            return "Unknown Source"
        
        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove common prefixes
        domain = domain.replace('www.', '')
        
        # Common news source mappings
        source_mappings = {
            'techcrunch.com': 'TechCrunch',
            'reuters.com': 'Reuters',
            'cnn.com': 'CNN',
            'bbc.com': 'BBC',
            'bbc.co.uk': 'BBC',
            'nytimes.com': 'The New York Times',
            'wsj.com': 'The Wall Street Journal',
            'washingtonpost.com': 'The Washington Post',
            'forbes.com': 'Forbes',
            'bloomberg.com': 'Bloomberg',
            'cnbc.com': 'CNBC',
            'theverge.com': 'The Verge',
            'arstechnica.com': 'Ars Technica',
            'wired.com': 'Wired',
            'engadget.com': 'Engadget',
            'mashable.com': 'Mashable',
            'venturebeat.com': 'VentureBeat',
            'zdnet.com': 'ZDNet',
            'cnet.com': 'CNET',
            'techradar.com': 'TechRadar',
            'theguardian.com': 'The Guardian',
            'independent.co.uk': 'The Independent',
            'ft.com': 'Financial Times',
            'economist.com': 'The Economist',
            'hbr.org': 'Harvard Business Review',
            'linkedin.com': 'LinkedIn',
            'medium.com': 'Medium',
            'substack.com': 'Substack',
            'github.com': 'GitHub',
            'stackoverflow.com': 'Stack Overflow',
            'reddit.com': 'Reddit',
            'twitter.com': 'Twitter',
            'x.com': 'X (Twitter)',
            'youtube.com': 'YouTube',
            'linkedin.com': 'LinkedIn'
        }
        
        # Check if we have a direct mapping
        if domain in source_mappings:
            return source_mappings[domain]
        
        # Extract from domain name (capitalize and clean)
        if '.' in domain:
            # Get the main part of the domain (before the first dot)
            main_part = domain.split('.')[0]
            # Capitalize and clean
            source = main_part.replace('-', ' ').replace('_', ' ').title()
            return source
        
        return "Unknown Source"
        
    except Exception as e:
        print(f"Error extracting source from URL {url}: {str(e)}")
        return "Unknown Source"


def summarize_article(url: str, title: str = '', snippet: str = '') -> str:
    """
    Fetch the article content from the URL and generate a 2-3 line summary.
    If content is insufficient, use title and snippet. If those are missing, use title alone.
    Always generate a meaningful summary from available information.
    """
    # Add variety to the summary generation by using different prompts
    summary_styles = [
        "Write a simple 2-3 line summary of this article for working professionals. Use easy words and focus on the main points and what they mean.",
        "Create a short 2-3 line summary of this article for business professionals. Use clear, simple language and highlight the main news.",
        "Give a brief 2-3 line overview of this article for LinkedIn professionals. Use everyday words and focus on practical takeaways.",
        "Write a 2-3 line summary of this article for professionals. Use simple words and focus on how this affects the industry.",
        "Summarize this article in 2-3 lines for working professionals. Use clear language and highlight trends and opportunities."
    ]
    
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        paragraphs = soup.find_all('p')
        text = ' '.join([p.get_text() for p in paragraphs])
        text = text.strip().replace('\n', ' ')
        if text and len(text) >= 100:
            prompt = (
                f"{random.choice(summary_styles)}\n\n"
                f"Article:\n{text[:3000]}"
            )
            summary = model.generate_content(prompt).text.strip()
            summary_lines = [line for line in summary.split('\n') if line.strip()]
            return '\n'.join(summary_lines[:3])
        # Fallback: use title and snippet
        elif title or snippet:
            prompt = (
                f"{random.choice(summary_styles)}\n\n"
                f"Title: {title}\nSnippet: {snippet}"
            )
            summary = model.generate_content(prompt).text.strip()
            summary_lines = [line for line in summary.split('\n') if line.strip()]
            return '\n'.join(summary_lines[:3])
        # Fallback: use title only
        elif title:
            prompt = (
                f"{random.choice(summary_styles)}\n\n"
                f"Title: {title}"
            )
            summary = model.generate_content(prompt).text.strip()
            summary_lines = [line for line in summary.split('\n') if line.strip()]
            return '\n'.join(summary_lines[:3])
        else:
            # Generate summary from URL/domain
            url_parts = url.split('/')
            if len(url_parts) > 2:
                domain = url_parts[2]
                prompt = f"{random.choice(summary_styles)} This article is from {domain} and covers technology, business, or industry news."
                summary = model.generate_content(prompt).text.strip()
                summary_lines = [line for line in summary.split('\n') if line.strip()]
                return '\n'.join(summary_lines[:3])
            else:
                return "Summary not available for this article."
    except Exception as e:
        print(f"Error summarizing article {url}: {str(e)}")
        # Last resort: generate summary from available information
        try:
            if title or snippet:
                prompt = (
                    f"{random.choice(summary_styles)}\n\n"
                    f"Title: {title}\nSnippet: {snippet}\nURL: {url}"
                )
                summary = model.generate_content(prompt).text.strip()
                summary_lines = [line for line in summary.split('\n') if line.strip()]
                return '\n'.join(summary_lines[:3])
            else:
                # Generate from URL/domain
                url_parts = url.split('/')
                if len(url_parts) > 2:
                    domain = url_parts[2]
                    prompt = f"{random.choice(summary_styles)} This article is from {domain} and covers technology, business, or industry news."
                    summary = model.generate_content(prompt).text.strip()
                    summary_lines = [line for line in summary.split('\n') if line.strip()]
                    return '\n'.join(summary_lines[:3])
                else:
                    return "Summary not available for this article."
        except:
            return "Summary not available for this article."


@dataclass
class NewsArticle:
    """Data class for news articles"""
    url: str
    title: str
    snippet: str
    source: str
    published_at: Optional[str] = None
    summary: Optional[str] = None
    is_relevant: bool = True


@dataclass
class Category:
    """Data class for news categories"""
    name: str
    keywords: List[str]
    articles: List[NewsArticle] = None
    
    def __post_init__(self):
        if self.articles is None:
            self.articles = []


class CategoryGeneratorAgent:
    """Agent responsible for generating up to 5 categories based on persona keywords"""
    
    def __init__(self):
        self.model = model
    
    async def generate_categories(self, 
                                general_persona_keywords: List[str],
                                content_persona_keywords: Optional[List[str]] = None,
                                network_persona_keywords: Optional[List[str]] = None) -> List[Category]:
        """
        Generate up to 5 unique categories, each composed of 2-3 words.
        Categories should be interesting, attention-grabbing, well-composed, and unique.
        """
        try:
            # Combine all persona keywords
            all_keywords = general_persona_keywords.copy()
            if content_persona_keywords:
                all_keywords.extend(content_persona_keywords)
            if network_persona_keywords:
                all_keywords.extend(network_persona_keywords)
            
            persona_description = f"Professional with keywords: {', '.join(all_keywords)}"
            
            prompt = f"""
You are a professional news category generator. Based on the following persona, generate exactly 5 unique news categories:

Persona: {persona_description}

Requirements:
1. Generate exactly 5 categories, each made of 2-3 words
2. Categories should be interesting, eye-catching, well-written, and unique
3. Each category should fit the person's work context and interests
4. Categories should be specific enough to find relevant news but broad enough to get results
5. Focus on work-related, industry-relevant categories
6. Avoid basic categories like "Technology News" or "Business Updates"
7. Make categories sound like professional LinkedIn content categories
8. Use simple, clear words that all professionals can understand

Format your response as a numbered list of exactly 5 categories, one per line:
1. [category 1]
2. [category 2]
3. [category 3]
4. [category 4]
5. [category 5]

Do not include any explanations or additional text.
"""
            
            response = self.model.generate_content(prompt)
            categories_text = response.text.strip()
            
            # Parse the numbered list
            category_names = []
            for line in categories_text.split('\n'):
                line = line.strip()
                if line and any(line.startswith(f"{i}.") for i in range(1, 6)):
                    # Extract the category part after the number
                    category = line.split('.', 1)[1].strip()
                    if category:
                        category_names.append(category)
            
            # Ensure we have exactly 5 categories
            if len(category_names) != 5:
                # Fallback categories if AI doesn't generate exactly 5
                primary_keyword = all_keywords[0] if all_keywords else 'technology'
                secondary_keyword = all_keywords[1] if len(all_keywords) > 1 else 'industry'
                
                fallback_categories = [
                    f"{primary_keyword} Innovation",
                    f"{primary_keyword} Leadership",
                    f"{secondary_keyword} Trends",
                    f"{primary_keyword} Development",
                    f"{secondary_keyword} Insights"
                ]
                category_names = fallback_categories[:5]
            
            # Generate keywords for each category
            categories = []
            for category_name in category_names[:5]:
                keywords = await self._generate_keywords_for_category(category_name, all_keywords)
                categories.append(Category(name=category_name, keywords=keywords))
            
            return categories
            
        except Exception as e:
            print(f"Error generating categories: {str(e)}")
            # Fallback categories
            primary_keyword = general_persona_keywords[0] if general_persona_keywords else 'technology'
            return [
                Category(name=f"{primary_keyword} Innovation", keywords=[primary_keyword, "innovation"]),
                Category(name=f"{primary_keyword} Leadership", keywords=[primary_keyword, "leadership"]),
                Category(name=f"{primary_keyword} Trends", keywords=[primary_keyword, "trends"]),
                Category(name=f"{primary_keyword} Development", keywords=[primary_keyword, "development"]),
                Category(name=f"{primary_keyword} Insights", keywords=[primary_keyword, "insights"])
            ]
    
    async def _generate_keywords_for_category(self, category_name: str, persona_keywords: List[str]) -> List[str]:
        """Generate relevant keywords for a specific category"""
        try:
            prompt = f"""
Generate 3-5 relevant keywords for the news category "{category_name}" based on these persona keywords: {', '.join(persona_keywords)}

Requirements:
1. Keywords should be specific to the category
2. Include terms that would help find recent, relevant news
3. Mix of broad and specific terms
4. Focus on work-related and industry-relevant terms
5. Use simple, clear words that professionals can understand

Return only the keywords, separated by commas, no explanations.
"""
            
            response = self.model.generate_content(prompt)
            keywords_text = response.text.strip()
            keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
            
            # Ensure we have at least 2 keywords
            if len(keywords) < 2:
                keywords = [category_name.lower(), "latest", "news"]
            
            return keywords[:5]  # Limit to 5 keywords
            
        except Exception as e:
            print(f"Error generating keywords for category {category_name}: {str(e)}")
            return [category_name.lower(), "latest", "news"]


class NewsRetrievalAgent:
    """Agent responsible for keyword extraction and news retrieval"""
    
    def __init__(self):
        self.serpapi_key = os.environ.get("SERPAPI_KEY")
    
    async def fetch_news_for_category(self, category: Category) -> Tuple[List[NewsArticle], bool]:
        """
        Fetch up to 5 recent news articles for a category.
        Returns (articles, success_flag) where success_flag indicates if relevant news was found.
        """
        try:
            if not self.serpapi_key:
                # Return placeholder news if no API key
                placeholder_articles = [
                    NewsArticle(
                        url=f"https://example.com/news/{category.name.lower().replace(' ', '-')}-{i+1}",
                        title=f"Latest {category.name} News #{i+1}",
                        snippet=f"Recent developments in {category.name}",
                        source="Example News"
                    ) for i in range(5)
                ]
                return placeholder_articles, True
            
            # Generate search query from category keywords
            search_query = await self._generate_search_query(category)
            
            # Fetch news using SERP API
            articles = await self._fetch_from_serpapi(search_query, 5)
            
            # Validate relevance
            relevant_articles = []
            for article in articles:
                is_relevant = await self._validate_relevance(article, category)
                article.is_relevant = is_relevant
                if is_relevant:
                    relevant_articles.append(article)
            
            # If we have at least 2 relevant articles, consider it successful
            success = len(relevant_articles) >= 2
            
            # Return only relevant articles, limit to 3-5
            relevant_articles = relevant_articles[:5]  # Max 5
            
            return relevant_articles, success
            
        except Exception as e:
            print(f"Error fetching news for category {category.name}: {str(e)}")
            return [], False

    def fetch_news_for_category_sync(self, category: Category) -> Tuple[List[NewsArticle], bool]:
        """
        Synchronous version of fetch_news_for_category for use with ThreadPoolExecutor.
        """
        try:
            if not self.serpapi_key:
                # Return placeholder news if no API key
                placeholder_articles = [
                    NewsArticle(
                        url=f"https://example.com/news/{category.name.lower().replace(' ', '-')}-{i+1}",
                        title=f"Latest {category.name} News #{i+1}",
                        snippet=f"Recent developments in {category.name}",
                        source="Example News"
                    ) for i in range(5)
                ]
                return placeholder_articles, True
            
            # Generate search query from category keywords
            search_query = self._generate_search_query_sync(category)
            
            # Fetch news using SERP API
            articles = self._fetch_from_serpapi_sync(search_query, 5)
            
            # Validate relevance
            relevant_articles = []
            for article in articles:
                is_relevant = self._validate_relevance_sync(article, category)
                article.is_relevant = is_relevant
                if is_relevant:
                    relevant_articles.append(article)
            
            # If we have at least 2 relevant articles, consider it successful
            success = len(relevant_articles) >= 2
            
            # Return only relevant articles, limit to 3-5
            relevant_articles = relevant_articles[:5]  # Max 5
            
            return relevant_articles, success
            
        except Exception as e:
            print(f"Error fetching news for category {category.name}: {str(e)}")
            return [], False
    
    async def _generate_search_query(self, category: Category) -> str:
        """Generate an optimized search query from category keywords"""
        try:
            # Combine category name and keywords for better search results
            query_parts = [category.name]
            query_parts.extend(category.keywords[:2])  # Use top 2 keywords
            
            # Add recent news terms
            recent_terms = ["latest news", "recent developments", "breaking news"]
            query_parts.append(random.choice(recent_terms))
            
            return " ".join(query_parts)
            
        except Exception as e:
            print(f"Error generating search query: {str(e)}")
            return f"{category.name} latest news"

    def _generate_search_query_sync(self, category: Category) -> str:
        """Synchronous version of _generate_search_query"""
        try:
            # Combine category name and keywords for better search results
            query_parts = [category.name]
            query_parts.extend(category.keywords[:2])  # Use top 2 keywords
            
            # Add recent news terms
            recent_terms = ["latest news", "recent developments", "breaking news"]
            query_parts.append(random.choice(recent_terms))
            
            return " ".join(query_parts)
            
        except Exception as e:
            print(f"Error generating search query: {str(e)}")
            return f"{category.name} latest news"
    
    async def _fetch_from_serpapi(self, query: str, count: int) -> List[NewsArticle]:
        """Fetch news articles from SERP API"""
        try:
            # Try multiple search strategies to get different results
            search_strategies = [
                {
                    'api_key': self.serpapi_key,
                    'q': query,
                    'engine': 'google',
                    'tbm': 'nws',  # News search
                    'num': count,
                    'tbs': 'qdr:w'  # Last week
                },
                {
                    'api_key': self.serpapi_key,
                    'q': f"{query} recent",
                    'engine': 'google',
                    'tbm': 'nws',
                    'num': count,
                    'tbs': 'qdr:d'  # Last day
                }
            ]
            
            all_articles = []
            
            for params in search_strategies:
                try:
                    response = requests.get('https://serpapi.com/search', params=params, timeout=10)
                    response.raise_for_status()
                    data = response.json()
                    
                    articles = []
                    
                    # Try news results first
                    if 'news_results' in data and data['news_results']:
                        for item in data['news_results'][:count]:
                            source = extract_source_from_url(item.get('link', ''))
                            article = NewsArticle(
                                url=item.get('link', ''),
                                title=item.get('title', ''),
                                snippet=item.get('snippet', ''),
                                source=source,
                                published_at=item.get('date', None)
                            )
                            articles.append(article)
                    
                    # Try organic results as fallback
                    elif 'organic_results' in data and data['organic_results']:
                        for item in data['organic_results'][:count]:
                            # Only include if it looks like a news article
                            title = item.get('title', '').lower()
                            url = item.get('link', '').lower()
                            if any(term in title or term in url for term in ['news', 'article', 'blog', 'tech', 'business']):
                                source = extract_source_from_url(item.get('link', ''))
                                article = NewsArticle(
                                    url=item.get('link', ''),
                                    title=item.get('title', ''),
                                    snippet=item.get('snippet', ''),
                                    source=source,
                                    published_at=item.get('date', None)
                                )
                                articles.append(article)
                    
                    all_articles.extend(articles)
                    
                except Exception as e:
                    print(f"Strategy failed for query '{query}': {str(e)}")
                    continue
            
            # Remove duplicates and limit to 3-5 articles
            unique_articles = []
            seen_urls = set()
            for article in all_articles:
                if article.url not in seen_urls:
                    seen_urls.add(article.url)
                    unique_articles.append(article)
                if len(unique_articles) >= 5:  # Max 5 articles
                    break
            
            return unique_articles[:5]  # Return 3-5 articles
            
        except Exception as e:
            print(f"Error fetching from SERP API: {str(e)}")
            return []
    
    async def _validate_relevance(self, article: NewsArticle, category: Category) -> bool:
        """Validate if an article is relevant to the category"""
        try:
            # Enhanced keyword-based validation with stricter rules
            category_terms = [category.name.lower()] + [kw.lower() for kw in category.keywords]
            article_text = f"{article.title.lower()} {article.snippet.lower()}"
            
            # Calculate relevance score
            relevance_score = 0
            
            # Check for exact category name matches (highest weight)
            if category.name.lower() in article_text:
                relevance_score += 3
            
            # Check for keyword matches
            for term in category_terms:
                if term in article_text:
                    relevance_score += 1
            
            # Check for domain-specific relevance
            url_lower = article.url.lower()
            if any(domain in url_lower for domain in ['android', 'kotlin', 'java', 'mobile', 'app', 'developer']):
                relevance_score += 1
            
            # Stricter threshold: require at least 2 points for relevance
            return relevance_score >= 2
            
        except Exception as e:
            print(f"Error validating relevance: {str(e)}")
            return False  # Default to not relevant if validation fails

    def _fetch_from_serpapi_sync(self, query: str, count: int) -> List[NewsArticle]:
        """Synchronous version of _fetch_from_serpapi"""
        try:
            # Try multiple search strategies to get different results
            search_strategies = [
                {
                    'api_key': self.serpapi_key,
                    'q': query,
                    'engine': 'google',
                    'tbm': 'nws',  # News search
                    'num': count,
                    'tbs': 'qdr:w'  # Last week
                },
                {
                    'api_key': self.serpapi_key,
                    'q': f"{query} recent",
                    'engine': 'google',
                    'tbm': 'nws',
                    'num': count,
                    'tbs': 'qdr:d'  # Last day
                }
            ]
            
            all_articles = []
            
            for params in search_strategies:
                try:
                    response = requests.get('https://serpapi.com/search', params=params, timeout=10)
                    response.raise_for_status()
                    data = response.json()
                    
                    articles = []
                    
                    # Try news results first
                    if 'news_results' in data and data['news_results']:
                        for item in data['news_results'][:count]:
                            source = extract_source_from_url(item.get('link', ''))
                            article = NewsArticle(
                                url=item.get('link', ''),
                                title=item.get('title', ''),
                                snippet=item.get('snippet', ''),
                                source=source,
                                published_at=item.get('date', None)
                            )
                            articles.append(article)
                    
                    # Try organic results as fallback
                    elif 'organic_results' in data and data['organic_results']:
                        for item in data['organic_results'][:count]:
                            # Only include if it looks like a news article
                            title = item.get('title', '').lower()
                            url = item.get('link', '').lower()
                            if any(term in title or term in url for term in ['news', 'article', 'blog', 'tech', 'business']):
                                source = extract_source_from_url(item.get('link', ''))
                                article = NewsArticle(
                                    url=item.get('link', ''),
                                    title=item.get('title', ''),
                                    snippet=item.get('snippet', ''),
                                    source=source,
                                    published_at=item.get('date', None)
                                )
                                articles.append(article)
                    
                    all_articles.extend(articles)
                    
                except Exception as e:
                    print(f"Strategy failed for query '{query}': {str(e)}")
                    continue
            
            # Remove duplicates and limit to 3-5 articles
            unique_articles = []
            seen_urls = set()
            for article in all_articles:
                if article.url not in seen_urls:
                    seen_urls.add(article.url)
                    unique_articles.append(article)
                if len(unique_articles) >= 5:  # Max 5 articles
                    break
            
            return unique_articles[:5]  # Return 3-5 articles
            
        except Exception as e:
            print(f"Error fetching from SERP API: {str(e)}")
            return []

    def _validate_relevance_sync(self, article: NewsArticle, category: Category) -> bool:
        """Synchronous version of _validate_relevance"""
        try:
            # Enhanced keyword-based validation with stricter rules
            category_terms = [category.name.lower()] + [kw.lower() for kw in category.keywords]
            article_text = f"{article.title.lower()} {article.snippet.lower()}"
            
            # Calculate relevance score
            relevance_score = 0
            
            # Check for exact category name matches (highest weight)
            if category.name.lower() in article_text:
                relevance_score += 3
            
            # Check for keyword matches
            for term in category_terms:
                if term in article_text:
                    relevance_score += 1
            
            # Check for domain-specific relevance
            url_lower = article.url.lower()
            if any(domain in url_lower for domain in ['android', 'kotlin', 'java', 'mobile', 'app', 'developer']):
                relevance_score += 1
            
            # Stricter threshold: require at least 2 points for relevance
            return relevance_score >= 2
            
        except Exception as e:
            print(f"Error validating relevance: {str(e)}")
            return False  # Default to not relevant if validation fails


class FallbackAgent:
    """Agent responsible for broader search when initial news retrieval fails"""
    
    def __init__(self):
        self.serpapi_key = os.environ.get("SERPAPI_KEY")
    
    async def fetch_fallback_news(self, category: Category) -> List[NewsArticle]:
        """
        Perform broader search beyond initial credible sources to find relevant news.
        """
        try:
            if not self.serpapi_key:
                # Return placeholder news if no API key
                return [
                    NewsArticle(
                        url=f"https://example.com/fallback/{category.name.lower().replace(' ', '-')}-{i+1}",
                        title=f"Fallback {category.name} News #{i+1}",
                        snippet=f"Alternative news sources for {category.name}",
                        source="Fallback News"
                    ) for i in range(5)
                ]
            
            # Generate broader search queries
            broader_queries = await self._generate_broader_queries(category)
            
            all_articles = []
            
            for query in broader_queries:
                try:
                    # Use broader search parameters
                    params = {
                        'api_key': self.serpapi_key,
                        'q': query,
                        'engine': 'google',
                        'num': 3,  # Fewer results per query to get variety
                        'tbs': 'qdr:m'  # Last month for broader time range
                    }
                    
                    response = requests.get('https://serpapi.com/search', params=params, timeout=10)
                    response.raise_for_status()
                    data = response.json()
                    
                    articles = []
                    
                    # Try both news and organic results
                    if 'news_results' in data and data['news_results']:
                        for item in data['news_results'][:3]:
                            source = extract_source_from_url(item.get('link', ''))
                            article = NewsArticle(
                                url=item.get('link', ''),
                                title=item.get('title', ''),
                                snippet=item.get('snippet', ''),
                                source=source,
                                published_at=item.get('date', None)
                            )
                            articles.append(article)
                    
                    elif 'organic_results' in data and data['organic_results']:
                        for item in data['organic_results'][:3]:
                            source = extract_source_from_url(item.get('link', ''))
                            article = NewsArticle(
                                url=item.get('link', ''),
                                title=item.get('title', ''),
                                snippet=item.get('snippet', ''),
                                source=source,
                                published_at=item.get('date', None)
                            )
                            articles.append(article)
                    
                    all_articles.extend(articles)
                    
                except Exception as e:
                    print(f"Fallback query failed: {str(e)}")
                    continue
            
            # Remove duplicates and limit to 3-5 article
            unique_articles = []
            seen_urls = set()
            for article in all_articles:
                if article.url not in seen_urls:
                    seen_urls.add(article.url)
                    unique_articles.append(article)
                if len(unique_articles) >= 5:
                    break
            
            return unique_articles[:5]  # Return 3-5 articles
            
        except Exception as e:
            print(f"Error in fallback news retrieval: {str(e)}")
            return []

    def fetch_fallback_news_sync(self, category: Category) -> List[NewsArticle]:
        """
        Synchronous version of fetch_fallback_news for use with ThreadPoolExecutor.
        """
        try:
            if not self.serpapi_key:
                # Return placeholder news if no API key
                return [
                    NewsArticle(
                        url=f"https://example.com/fallback/{category.name.lower().replace(' ', '-')}-{i+1}",
                        title=f"Fallback {category.name} News #{i+1}",
                        snippet=f"Alternative news sources for {category.name}",
                        source="Fallback News"
                    ) for i in range(5)
                ]
            
            # Generate broader search queries
            broader_queries = self._generate_broader_queries_sync(category)
            
            all_articles = []
            
            for query in broader_queries:
                try:
                    # Use broader search parameters
                    params = {
                        'api_key': self.serpapi_key,
                        'q': query,
                        'engine': 'google',
                        'num': 3,  # Fewer results per query to get variety
                        'tbs': 'qdr:m'  # Last month for broader time range
                    }
                    
                    response = requests.get('https://serpapi.com/search', params=params, timeout=10)
                    response.raise_for_status()
                    data = response.json()
                    
                    articles = []
                    
                    # Try both news and organic results
                    if 'news_results' in data and data['news_results']:
                        for item in data['news_results'][:3]:
                            source = extract_source_from_url(item.get('link', ''))
                            article = NewsArticle(
                                url=item.get('link', ''),
                                title=item.get('title', ''),
                                snippet=item.get('snippet', ''),
                                source=source,
                                published_at=item.get('date', None)
                            )
                            articles.append(article)
                    
                    elif 'organic_results' in data and data['organic_results']:
                        for item in data['organic_results'][:3]:
                            source = extract_source_from_url(item.get('link', ''))
                            article = NewsArticle(
                                url=item.get('link', ''),
                                title=item.get('title', ''),
                                snippet=item.get('snippet', ''),
                                source=source,
                                published_at=item.get('date', None)
                            )
                            articles.append(article)
                    
                    all_articles.extend(articles)
                    
                except Exception as e:
                    print(f"Fallback query failed: {str(e)}")
                    continue
            
            # Remove duplicates and limit to 3-5 articles
            unique_articles = []
            seen_urls = set()
            for article in all_articles:
                if article.url not in seen_urls:
                    seen_urls.add(article.url)
                    unique_articles.append(article)
                if len(unique_articles) >= 5:
                    break
            
            return unique_articles[:5]  # Return 3-5 articles
            
        except Exception as e:
            print(f"Error in fallback news retrieval: {str(e)}")
            return []
    
    async def _generate_broader_queries(self, category: Category) -> List[str]:
        """Generate broader search queries for fallback"""
        try:
            # Create variations of the category name and keywords
            base_terms = [category.name] + category.keywords[:2]
            
            broader_queries = []
            
            # Add industry-specific terms
            industry_terms = ["industry", "sector", "market", "trends", "developments"]
            for base_term in base_terms:
                for industry_term in industry_terms:
                    broader_queries.append(f"{base_term} {industry_term}")
            
            # Add time-based variations
            time_terms = ["recent", "latest", "current", "new", "emerging"]
            for base_term in base_terms:
                for time_term in time_terms:
                    broader_queries.append(f"{time_term} {base_term}")
            
            # Shuffle and limit to 3 queries to avoid too many API calls
            random.shuffle(broader_queries)
            return broader_queries[:3]
            
        except Exception as e:
            print(f"Error generating broader queries: {str(e)}")
            return [f"{category.name} industry", f"{category.name} trends", f"{category.name} developments"]

    def _generate_broader_queries_sync(self, category: Category) -> List[str]:
        """Synchronous version of _generate_broader_queries"""
        try:
            # Create variations of the category name and keywords
            base_terms = [category.name] + category.keywords[:2]
            
            broader_queries = []
            
            # Add industry-specific terms
            industry_terms = ["industry", "sector", "market", "trends", "developments"]
            for base_term in base_terms:
                for industry_term in industry_terms:
                    broader_queries.append(f"{base_term} {industry_term}")
            
            # Add time-based variations
            time_terms = ["recent", "latest", "current", "new", "emerging"]
            for base_term in base_terms:
                for time_term in time_terms:
                    broader_queries.append(f"{time_term} {base_term}")
            
            # Shuffle and limit to 3 queries to avoid too many API calls
            random.shuffle(broader_queries)
            return broader_queries[:3]
            
        except Exception as e:
            print(f"Error generating broader queries: {str(e)}")
            return [f"{category.name} industry", f"{category.name} trends", f"{category.name} developments"]


class SupervisorAgent:
    """Supervisor Agent that manages and coordinates all sub-agents"""
    
    def __init__(self):
        self.category_agent = CategoryGeneratorAgent()
        self.news_agent = NewsRetrievalAgent()
        self.fallback_agent = FallbackAgent()
    
    async def get_trending_news(self,
                               general_persona_keywords: List[str],
                               content_persona_keywords: Optional[List[str]] = None,
                               network_persona_keywords: Optional[List[str]] = None,
                               user_categories: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Main method that coordinates the entire agentic workflow.
        Returns the complete output formatted like the current /trending-news response.
        """
        try:
            # Step 1: Generate or use categories
            if user_categories and len(user_categories) > 0:
                # Use user-provided categories
                categories = []
                for category_name in user_categories[:5]:  # Limit to 5 categories
                    keywords = await self.category_agent._generate_keywords_for_category(
                        category_name, general_persona_keywords
                    )
                    categories.append(Category(name=category_name, keywords=keywords))
            else:
                # Generate categories using the CategoryGeneratorAgent
                categories = await self.category_agent.generate_categories(
                    general_persona_keywords,
                    content_persona_keywords,
                    network_persona_keywords
                )
            
            print(f"Generated {len(categories)} categories")
            
            # Ensure we have at least some categories
            if not categories:
                print("No categories generated, creating fallback categories")
                primary_keyword = general_persona_keywords[0] if general_persona_keywords else 'technology'
                categories = [
                    Category(name=f"{primary_keyword} Development", keywords=[primary_keyword, "development"]),
                    Category(name=f"{primary_keyword} Innovation", keywords=[primary_keyword, "innovation"]),
                    Category(name=f"{primary_keyword} Trends", keywords=[primary_keyword, "trends"])
                ]
            
            # Step 2: Process categories in parallel
            final_results = []
            
            print(f"Processing {len(categories)} categories in parallel...")
            
            # Define function to process a single category
            def process_category(category):
                print(f"Processing category: {category.name}")
                
                # Step 2a: Try initial news retrieval
                articles, success = self.news_agent.fetch_news_for_category_sync(category)
                print(f"  Initial retrieval: {len(articles)} articles, success={success}")
                
                # Step 2b: If initial retrieval failed or found insufficient relevant news, use fallback
                if not success or len(articles) < 3:
                    print(f"  Initial retrieval insufficient for {category.name}, using fallback")
                    fallback_articles = self.fallback_agent.fetch_fallback_news_sync(category)
                    print(f"  Fallback retrieval: {len(fallback_articles)} articles")
                    
                    # Combine articles (deduplication will be done at the end)
                    all_articles = articles + fallback_articles
                    articles = all_articles[:5]  # Limit to 5 articles
                    print(f"  Final articles after combination: {len(articles)}")
                
                # Step 2c: Generate summaries for articles (only if we have relevant articles)
                summarized_articles = []
                if articles:  # Only process if we have articles
                    for article in articles:
                        # Generate summary
                        summary = summarize_article(article.url, article.title, article.snippet)
                        article.summary = summary
                        
                        summarized_articles.append({
                            "url": article.url,
                            "title": article.title,
                            "published_at": article.published_at,
                            "summary": summary,
                            "source": article.source
                        })
                        
                        # Limit to 3-5 articles per category
                        if len(summarized_articles) >= 5:
                            break
                
                # Step 2d: Return category result (only if we have relevant articles)
                if summarized_articles and len(summarized_articles) >= 3:  # Minimum 3 articles
                    result = {
                        "category_name": category.name,
                        "articles": summarized_articles
                    }
                    print(f"  Added category '{category.name}' with {len(summarized_articles)} articles")
                    return result
                else:
                    print(f"  Skipped category '{category.name}' - insufficient relevant articles ({len(summarized_articles) if summarized_articles else 0})")
                    return None
            
            # Process categories in parallel using ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=min(4, len(categories))) as executor:
                category_futures = [executor.submit(process_category, category) for category in categories]
                category_results = [future.result() for future in category_futures]
            
            # Filter out None results and deduplicate URLs across all results
            used_urls = set()
            deduplicated_results = []
            
            for result in category_results:
                if result is not None:
                    # Deduplicate articles within this category
                    unique_articles = []
                    for article in result["articles"]:
                        if article["url"] not in used_urls:
                            used_urls.add(article["url"])
                            unique_articles.append(article)
                        if len(unique_articles) >= 5:  # Max 5 articles per category
                            break
                    
                    if len(unique_articles) >= 3:  # Still need minimum 3 articles
                        result["articles"] = unique_articles
                        deduplicated_results.append(result)
            
            final_results = deduplicated_results
            
            # Step 3: Return formatted results
            print(f"Final result: {len(final_results)} categories with news")
            return {"news": final_results}
            
        except Exception as e:
            print(f"Error in SupervisorAgent: {str(e)}")
            # Return fallback response
            primary_keyword = general_persona_keywords[0] if general_persona_keywords else 'technology'
            return {
                "news": [{
                    "category_name": f"{primary_keyword} News",
                    "articles": [{
                        "url": f"https://example.com/news/{primary_keyword.lower()}-{i+1}",
                        "title": f"Latest {primary_keyword} News #{i+1}",
                        "summary": f"Recent developments in {primary_keyword} industry",
                        "source": "Example News"
                    } for i in range(5)]
                }]
            }


# Global supervisor instance
supervisor = SupervisorAgent()


async def get_trending_news_agentic(general_persona_keywords: List[str],
                                   content_persona_keywords: Optional[List[str]] = None,
                                   network_persona_keywords: Optional[List[str]] = None,
                                   categories: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Main function to get trending news using the agentic architecture.
    This function maintains the same interface as the original trending-news endpoint.
    """
    return await supervisor.get_trending_news(
        general_persona_keywords=general_persona_keywords,
        content_persona_keywords=content_persona_keywords,
        network_persona_keywords=network_persona_keywords,
        user_categories=categories
    ) 