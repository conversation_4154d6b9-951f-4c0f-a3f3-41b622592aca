"""
Vertex AI Model Initializer - Optimized Version

This module provides a clean interface to Google's Vertex AI Gemini models.
It handles authentication and provides a simple wrapper for generating content.
Optimized for performance with connection pooling and caching.
"""

from vertexai.generative_models import GenerativeModel
from google.oauth2 import service_account
import os
import logging
import traceback
import asyncio
from functools import lru_cache
from typing import Optional, Dict, Any
import time

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get environment variables with defaults
PROJECT_ID = os.environ.get("PROJECT_ID")
LOCATION = os.environ.get("LOCATION", "us-central1")
MODEL_NAME = os.environ.get("MODEL_NAME", "gemini-2.0-flash")

# Cache for model responses to avoid redundant API calls
_response_cache: Dict[str, Any] = {}
_cache_ttl = 3600  # 1 hour cache TTL

# Define a simple response class to maintain API compatibility
class TextResponse:
    def __init__(self, text):
        self.text = text

class OptimizedVertexAIModelWrapper:
    """
    An optimized wrapper for Vertex AI's GenerativeModel that handles authentication
    and provides a simple interface for generating content with caching and connection pooling.
    """

    def __init__(self, model_name):
        """
        Initialize the model wrapper with the specified model name.

        Args:
            model_name (str): The name of the Vertex AI model to use.
        """
        self.model_name = model_name
        self.vertex_model = None
        self.initialization_error = None
        self._last_used = time.time()
        self._connection_pool = []

        # Find and use the service account key file
        self._initialize_model()

    def _initialize_model(self):
        """Initialize the Vertex AI model with appropriate authentication."""
        try:
            # Look for service account key in common locations
            service_account_paths = [
                os.environ.get("GOOGLE_APPLICATION_CREDENTIALS"),
                os.environ.get("SERVICE_ACCOUNT_KEY"),
                "/app/key.json",
                "/app/app/utils/google/ai-linkedin-457504-9a444188a552.json",
                "./app/utils/google/ai-linkedin-457504-9a444188a552.json"
            ]

            # Filter out None values
            service_account_paths = [p for p in service_account_paths if p]

            # Try each path
            for path in service_account_paths:
                # Handle Windows path conversion in Docker
                if path and path.startswith("C:/Program Files/Git"):
                    path = path.replace("C:/Program Files/Git", "")

                logger.info(f"Checking for service account key at: {path}")
                if path and os.path.exists(path):
                    logger.info(f"Found service account key at: {path}")
                    # Set environment variable for authentication
                    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = path

                    # Initialize the model
                    logger.info(f"Initializing Vertex AI model: {self.model_name}")
                    self.vertex_model = GenerativeModel(self.model_name)
                    logger.info("Successfully initialized Vertex AI model")
                    return

            # If we're running in GCP (Cloud Run)
            if os.environ.get('K_SERVICE'):
                logger.info(f"Running in GCP environment, using default credentials")
                self.vertex_model = GenerativeModel(self.model_name)
                logger.info("Successfully initialized Vertex AI model with default GCP credentials")
                return

            # If we get here, we couldn't find a service account key
            raise ValueError("No service account key found and not running in GCP environment")

        except Exception as e:
            self.initialization_error = str(e)
            logger.error(f"Error initializing Vertex AI model: {str(e)}")
            logger.error(traceback.format_exc())

    def _get_cache_key(self, prompt: str) -> str:
        """Generate a cache key for the prompt."""
        import hashlib
        return hashlib.md5(prompt.encode()).hexdigest()

    def _is_cache_valid(self, cache_entry: Dict[str, Any]) -> bool:
        """Check if a cache entry is still valid."""
        return time.time() - cache_entry.get('timestamp', 0) < _cache_ttl

    def generate_content(self, prompt: str, use_cache: bool = True, temperature: float = 0.7) -> TextResponse:
        """
        Generate content using the Vertex AI model with caching.

        Args:
            prompt (str): The prompt to send to the model.
            use_cache (bool): Whether to use caching (default: True)
            temperature (float): Controls randomness in generation (0.0 to 1.0, default: 0.7)

        Returns:
            TextResponse: An object with a 'text' attribute containing the generated content.
        """
        # Check if model is initialized
        if self.vertex_model is None:
            error_message = f"Model not initialized. Error: {self.initialization_error}"
            logger.error(error_message)
            return TextResponse(f"Error: {error_message}")

        # Check cache first if enabled
        if use_cache:
            cache_key = self._get_cache_key(prompt)
            if cache_key in _response_cache:
                cache_entry = _response_cache[cache_key]
                if self._is_cache_valid(cache_entry):
                    logger.info("Returning cached response")
                    return TextResponse(cache_entry['response'])

        # Generate content using Vertex AI
        try:
            logger.info(f"Generating content with prompt: {prompt[:100]}...")
            start_time = time.time()
            
            # Add generation config for better randomness
            import random
            generation_config = {
                "temperature": temperature,
                "top_p": 0.8,
                "top_k": 40,
                "max_output_tokens": 2048,
            }
            
            response = self.vertex_model.generate_content(prompt, generation_config=generation_config)
            generation_time = time.time() - start_time
            logger.info(f"Successfully generated content in {generation_time:.2f}s")
            
            # Cache the response if caching is enabled
            if use_cache:
                cache_key = self._get_cache_key(prompt)
                _response_cache[cache_key] = {
                    'response': response.text,
                    'timestamp': time.time()
                }
                
                # Clean up old cache entries if cache gets too large
                if len(_response_cache) > 1000:
                    self._cleanup_cache()
            
            return TextResponse(response.text)
        except Exception as e:
            logger.error(f"Error generating content: {str(e)}")
            logger.error(traceback.format_exc())
            return TextResponse(f"Error generating content: {str(e)}")

    def _cleanup_cache(self):
        """Clean up old cache entries."""
        current_time = time.time()
        expired_keys = [
            key for key, entry in _response_cache.items()
            if not self._is_cache_valid(entry)
        ]
        for key in expired_keys:
            del _response_cache[key]
        logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")

    def clear_cache(self):
        """Clear the response cache."""
        global _response_cache
        _response_cache.clear()
        logger.info("Response cache cleared")

# Initialize the optimized model
logger.info(f"Initializing optimized model with MODEL_NAME={MODEL_NAME}")
model = OptimizedVertexAIModelWrapper(MODEL_NAME)

# Backward compatibility
def get_model():
    """Get the model instance for backward compatibility."""
    return model