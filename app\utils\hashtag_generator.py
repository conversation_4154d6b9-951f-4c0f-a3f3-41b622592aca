from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES
from functools import lru_cache
import hashlib
import requests
import os
import json
from typing import List, Dict, Any
import re

# Cache for hashtag generation
_hashtag_cache = {}

@lru_cache(maxsize=1000)
def _get_cache_key(post_content: str, interests: tuple = None) -> str:
    """Generate a cache key for hashtag generation."""
    content_hash = hashlib.md5(post_content.encode()).hexdigest()
    interests_hash = hashlib.md5(str(interests).encode()).hexdigest() if interests else "none"
    return f"{content_hash}_{interests_hash}"

def fetch_popular_hashtags(general_persona_keywords: List[str], 
                          content_persona_keywords: List[str] = None, 
                          network_persona_keywords: List[str] = None) -> dict:
    """
    Fetch popular hashtags using AI for natural, LinkedIn-appropriate, professional hashtags based on persona/content/network keywords.
    Returns a dict with two lists:
      - persona_hashtags: 4 persona-specific, professional, actionable hashtags
      - universal_hashtags: 4 universal/trending, but professional and persona-aligned hashtags
    Enhanced: Uses AI to generate hashtags that are relevant, professional, and commonly used on LinkedIn.
    """
    # Combine all persona keywords
    persona_keywords = general_persona_keywords.copy() if general_persona_keywords else []
    if content_persona_keywords:
        persona_keywords.extend(content_persona_keywords)
    if network_persona_keywords:
        persona_keywords.extend(network_persona_keywords)
    
    # Use AI to generate up to 8 natural, LinkedIn-appropriate hashtags
    keywords_str = ', '.join(persona_keywords)
    ai_prompt = (
        f"Generate up to 8 highly relevant, professional, and commonly used LinkedIn hashtags based on these persona, content, and network keywords: {keywords_str}.\n"
        "Requirements:\n"
        "- Only include hashtags that are natural, widely used, and appropriate for LinkedIn.\n"
        "- Avoid generic adjectives (like #Professional, #Analytical) and uncommon phrases.\n"
        "- Prefer hashtags that are actionable, industry-relevant, or engagement-focused.\n"
        "- Do not include hashtags like #trending, #viral, #instagood, #love, #photooftheday, #like, #follow, #success, #motivation, #business, #career, #linkedin, #innovation, #management, #news, #update, #socialmedia.\n"
        "- Return only the hashtags, separated by spaces, no explanations.\n"
        "- Example: #Storytelling #BrandVoice #AudienceEngagement #ContentStrategy #ThoughtLeadership #Mentorship #Networking #GrowthMindset"
    )
    response = model.generate_content(ai_prompt)
    hashtags_text = response.text.strip()
    # Extract hashtags from AI response
    def _extract_hashtags_from_text(text: str) -> list:
        hashtag_pattern = r'#\w+'
        hashtags = re.findall(hashtag_pattern, text)
        # Clean and validate hashtags
        cleaned_hashtags = []
        for hashtag in hashtags:
            clean_hashtag = hashtag.rstrip('.,!?;:')
            if clean_hashtag.startswith('#') and len(clean_hashtag) > 1:
                cleaned_hashtags.append(clean_hashtag)
        return cleaned_hashtags
    hashtags = _extract_hashtags_from_text(hashtags_text)
    # Deduplicate and filter
    seen = set()
    deduped_hashtags = []
    for tag in hashtags:
        tag_lower = tag.lower()
        if tag_lower not in seen:
            seen.add(tag_lower)
            deduped_hashtags.append(tag)
        if len(deduped_hashtags) == 8:
            break
    # Fallback if not enough
    fallback_universal = ["#Storytelling", "#BrandVoice", "#AudienceEngagement", "#ContentStrategy", "#ThoughtLeadership", "#Mentorship", "#Networking", "#GrowthMindset", "#FutureOfWork", "#LeadershipTips", "#CareerDevelopment", "#RemoteWork", "#DigitalTransformation"]
    for tag in fallback_universal:
        tag_lower = tag.lower()
        if len(deduped_hashtags) < 8 and tag_lower not in set(t.lower() for t in deduped_hashtags):
            deduped_hashtags.append(tag)
    return {
        "persona_hashtags": deduped_hashtags[:4],
        "universal_hashtags": deduped_hashtags[4:8]
    }

def _fetch_all_hashtags_single_call(keywords: List[str], api_key: str) -> List[str]:
    """
    Fetch persona-relevant trending hashtags using a single optimized Serp API call.
    This function uses one perfectly crafted query to get both persona-related AND recent hashtags.
    """
    hashtags = []
    
    try:
        # Create a single, perfectly crafted search query that gets both persona-relevant AND recent hashtags
        keywords_str = " ".join(keywords)
        search_query = f"LinkedIn {keywords_str} trending popular viral hashtags today this week"
        
        url = "https://serpapi.com/search"
        params = {
            "q": search_query,
            "api_key": api_key,
            "engine": "google",
            "num": 25  # Get more results to ensure we find 8 quality hashtags
        }
        
        response = requests.get(url, params=params, timeout=15)
        response.raise_for_status()
        
        data = response.json()
        
        # Extract hashtags from search results
        if 'organic_results' in data:
            for result in data['organic_results']:
                # Check snippet for hashtags
                if 'snippet' in result:
                    snippet = result['snippet']
                    found_hashtags = _extract_hashtags_from_text(snippet)
                    # Filter hashtags to ensure they're relevant to persona
                    relevant_hashtags = _filter_persona_relevant_hashtags(found_hashtags, keywords)
                    hashtags.extend(relevant_hashtags)
                
                # Check title for hashtags
                if 'title' in result:
                    title = result['title']
                    found_hashtags = _extract_hashtags_from_text(title)
                    relevant_hashtags = _filter_persona_relevant_hashtags(found_hashtags, keywords)
                    hashtags.extend(relevant_hashtags)
                
                # Stop if we have enough quality hashtags
                if len(set(hashtags)) >= 12:
                    break
        
        # Also check for related searches that might contain hashtags
        if 'related_searches' in data:
            for related in data['related_searches']:
                if 'query' in related:
                    query = related['query']
                    found_hashtags = _extract_hashtags_from_text(query)
                    relevant_hashtags = _filter_persona_relevant_hashtags(found_hashtags, keywords)
                    hashtags.extend(relevant_hashtags)
                    
                    if len(set(hashtags)) >= 12:
                        break
                        
    except Exception as e:
        print(f"Error fetching hashtags: {str(e)}")
    
    # Remove duplicates and return exactly 8 hashtags
    unique_hashtags = list(dict.fromkeys(hashtags))
    return unique_hashtags[:8]

def _filter_persona_relevant_hashtags(hashtags: List[str], keywords: List[str]) -> List[str]:
    """
    Filter hashtags to ensure they're relevant to the persona keywords.
    Returns hashtags that are either:
    1. Directly related to persona keywords
    2. Professional/trending hashtags that would be relevant to the persona
    """
    relevant_hashtags = []
    
    # Convert keywords to lowercase for comparison
    keywords_lower = [kw.lower() for kw in keywords]
    
    # Professional hashtags that are always relevant
    professional_hashtags = {
        '#linkedin', '#networking', '#professional', '#career', '#business',
        '#leadership', '#innovation', '#growth', '#success', '#motivation',
        '#entrepreneurship', '#startup', '#tech', '#digital', '#future',
        '#strategy', '#management', '#teamwork', '#collaboration', '#expertise'
    }
    
    for hashtag in hashtags:
        hashtag_lower = hashtag.lower()
        
        # Check if hashtag is directly related to persona keywords
        is_keyword_related = any(keyword in hashtag_lower for keyword in keywords_lower)
        
        # Check if hashtag is a professional/trending hashtag
        is_professional = hashtag_lower in professional_hashtags
        
        # Check if hashtag contains trending terms
        trending_terms = ['trending', 'viral', 'popular', 'latest', 'new', 'today', 'week']
        is_trending = any(term in hashtag_lower for term in trending_terms)
        
        # Include hashtag if it meets any of the criteria
        if is_keyword_related or is_professional or is_trending:
            relevant_hashtags.append(hashtag)
    
    return relevant_hashtags

def _extract_hashtags_from_text(text: str) -> List[str]:
    """Extract hashtags from text using regex."""
    import re
    hashtag_pattern = r'#\w+'
    hashtags = re.findall(hashtag_pattern, text)
    
    # Clean and validate hashtags
    cleaned_hashtags = []
    for hashtag in hashtags:
        # Remove any trailing punctuation
        clean_hashtag = hashtag.rstrip('.,!?;:')
        # Ensure it starts with # and has at least one character
        if clean_hashtag.startswith('#') and len(clean_hashtag) > 1:
            cleaned_hashtags.append(clean_hashtag)
    
    return cleaned_hashtags

def _generate_ai_hashtags(keywords: List[str], count: int) -> List[str]:
    """Generate hashtags using AI when Serp API doesn't provide enough results."""
    try:
        keywords_str = ", ".join(keywords)
        prompt = f"""
        Generate {count} popular and relevant hashtags for LinkedIn posts related to these keywords: {keywords_str}
        
        Requirements:
        - Return only hashtags (words starting with #)
        - Make them relevant to the keywords provided
        - Use popular, trending hashtags
        - Separate hashtags with spaces
        - Don't include any other text, just hashtags
        
        Example format: #keyword1 #keyword2 #keyword3
        """
        
        response = model.generate_content(prompt)
        hashtags_text = response.text.strip()
        
        # Extract hashtags from AI response
        hashtags = _extract_hashtags_from_text(hashtags_text)
        
        return hashtags[:count]
        
    except Exception as e:
        print(f"Error generating AI hashtags: {str(e)}")
        # Fallback hashtags
        fallback_hashtags = [
            "#linkedin", "#networking", "#professional", "#career",
            "#business", "#leadership", "#innovation", "#growth"
        ]
        return fallback_hashtags[:count]

def generate_hashtags(post_content, interests=None, disable_cache=False, add_extra_hashtags=False):
    """Generate hashtags for a LinkedIn post, optionally considering user interests.

    Args:
        post_content: The content of the post
        interests: Optional list of user interests to consider for hashtag generation
        disable_cache: If True, skip cache lookup and always generate new hashtags
        add_extra_hashtags: If True, generate 2 additional hashtags for variety

    Returns:
        String of hashtags separated by spaces
    """
    # Create cache key
    interests_tuple = tuple(interests) if interests else None
    cache_key = _get_cache_key(post_content, interests_tuple)
    
    # Check cache first (unless caching is disabled)
    if not disable_cache and cache_key in _hashtag_cache:
        cached_result = _hashtag_cache[cache_key]
        if add_extra_hashtags:
            # Generate 2 additional hashtags when requested
            additional_hashtags = _generate_additional_hashtags(post_content, cached_result)
            return f"{cached_result} {additional_hashtags}".strip()
        return cached_result
    
    # If interests are provided, include them in the prompt
    if interests and len(interests) > 0:
        interests_str = ", ".join(interests)
        prompt = TEMPLATES["hashtag_suggestion_with_interests"].format(
            post=post_content,
            interests=interests_str
        )
    else:
        prompt = TEMPLATES["hashtag_suggestion"].format(post=post_content)

    response = model.generate_content(prompt)

    # Extract only the hashtags from the response
    hashtags_text = response.text.strip()

    # Ensure we're only returning hashtags
    hashtags_only = []
    for word in hashtags_text.split():
        if word.startswith('#'):
            hashtags_only.append(word)

    # If no hashtags were found, try to create them from the text
    if not hashtags_only:
        words = hashtags_text.split()
        hashtags_only = ['#' + word.strip(',.!?:;()[]{}"\'\'') for word in words if word.strip(',.!?:;()[]{}"\'\'')]

    result = ' '.join(hashtags_only)
    
    # Add extra hashtags if requested
    if add_extra_hashtags:
        additional_hashtags = _generate_additional_hashtags(post_content, result)
        result = f"{result} {additional_hashtags}".strip()
    
    # Cache the result (unless caching is disabled)
    if not disable_cache:
        _hashtag_cache[cache_key] = result
        
        # Clean up cache if it gets too large
        if len(_hashtag_cache) > 2000:
            _cleanup_hashtag_cache()
    
    return result

def _generate_additional_hashtags(post_content, existing_hashtags):
    """Generate 2 additional hashtags that are different from existing ones."""
    import time
    import random
    
    # Add randomization to ensure different hashtags each time
    randomization_phrases = [
        "Suggest 2 fresh and trending hashtags",
        "Generate 2 unique and engaging hashtags", 
        "Create 2 different professional hashtags",
        "Provide 2 alternative relevant hashtags",
        "Pick 2 new hashtags with high engagement potential"
    ]
    random_phrase = random.choice(randomization_phrases)
    timestamp_seed = str(int(time.time() * 1000))
    
    prompt = (
        f"{random_phrase} for this LinkedIn post that are different from these existing ones: {existing_hashtags}\n\n"
        f"Post content:\n{post_content}\n\n"
        f"Requirements:\n"
        f"- Return exactly 2 hashtags\n"
        f"- Make them different from existing hashtags\n"
        f"- Ensure they are relevant to the content\n"
        f"- Use professional LinkedIn-appropriate hashtags\n"
        f"- Return only hashtags separated by spaces, no explanations\n\n"
        f"Randomization seed: {timestamp_seed}\n"
    )
    
    response = model.generate_content(prompt)
    hashtags_text = response.text.strip()
    
    # Extract only hashtags
    hashtags_only = []
    for word in hashtags_text.split():
        if word.startswith('#'):
            hashtags_only.append(word)
    
    # If no hashtags found, create them from words
    if not hashtags_only:
        words = hashtags_text.split()
        hashtags_only = ['#' + word.strip(',.!?:;()[]{}"\'\'') for word in words[:2] if word.strip(',.!?:;()[]{}"\'\'')]
    
    # Return only first 2 hashtags
    return ' '.join(hashtags_only[:2])

def _cleanup_hashtag_cache():
    """Clean up the hashtag cache to prevent memory issues."""
    global _hashtag_cache
    # Keep only the most recent 1000 entries
    if len(_hashtag_cache) > 1000:
        # Convert to list and keep last 1000 items
        items = list(_hashtag_cache.items())
        _hashtag_cache = dict(items[-1000:])

def clear_hashtag_cache():
    """Clear the hashtag cache."""
    global _hashtag_cache
    _hashtag_cache.clear()